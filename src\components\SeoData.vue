<template>
  <Head>
    <Title>{{ pageData?.seoData?.title }}</Title>
    <Link rel="icon" href="/favicon.ico" />
    <Meta
      v-if="pageData?.seoData?.keyword"
      name="hotKeywords"
      :content="pageData?.seoData?.keyword"
    />
    <Meta name="description" :content="pageData?.seoData?.description" />
    <div
      v-if="pageData?.seoData?.headFirstScript"
      v-html="pageData?.seoData?.headFirstScript"
    ></div>
    <div
      v-if="pageData?.seoData?.headLastScript"
      v-html="pageData?.seoData?.headLastScript"
    ></div>
    <div
      v-if="pageData?.seoData?.bodyFirstContent"
      class="p-2"
      v-html="pageData?.seoData?.bodyFirstContent"
    ></div>
    <div
      v-if="pageData?.seoData?.bodyLastContent"
      class="p-2"
      v-html="pageData?.seoData?.bodyLastContent"
    ></div>
  </Head>
</template>
<script setup lang="ts" name="SeoData">
const props = defineProps({
  pageData: {
    type: Object,
    default: () => ({}),
  },
});
</script>
