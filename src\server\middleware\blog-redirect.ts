import { getRequestURL, sendRedirect } from "h3";

/**
 * Blog文章重定向中间件
 * 将旧的 /article?code=blogX 和 /h5/article?code=blogX 重定向到新的 /blog/blogX 和 /h5/blog/blogX
 */
export default defineEventHandler((event) => {
  const url = getRequestURL(event);
  
  // 检查是否是需要重定向的blog文章路径
  if (url.pathname === "/article" || url.pathname === "/h5/article") {
    const code = url.searchParams.get("code");
    
    // 只重定向blog文章（以blog开头的code）
    if (code && code.startsWith("blog")) {
      const isH5 = url.pathname.startsWith("/h5");
      const newPath = isH5 ? `/h5/blog/${code}` : `/blog/${code}`;
      
      // 使用301永久重定向
      return sendRedirect(event, newPath, 301);
    }
  }
});
