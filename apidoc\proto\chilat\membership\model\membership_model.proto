syntax = "proto3";
package chilat.membership;

option java_package = "com.chilat.rpc.membership.model";

import "common.proto";
import "common/business.proto";

message MembershipPageQueryResp {
  common.Result result = 1;
  common.Page page = 2;
  repeated MembershipModel data = 3;
}

// 商城用户信息
message MembershipModel {
  string id = 1; // 用户ID
  string username = 2; // 用户名
  string phone = 3; // 手机号
  string email = 4; // 邮箱
  int64 registerTime = 5; // 注册时间
  string inviteCode = 6; // 邀请码
  bool enabled = 7; // 是否启用
  string fromInviteCode = 8; // 来源邀请码
  string fromInviteUsername = 9; // 邀请人
  int32 isEmailVerified = 10; //是否邮箱已验证 1=已验证 0=未验证
}

message MembershipAddressResp {
  common.Result result = 1;
  repeated MembershipAddressModel data = 2;
}

message MembershipAddressModel {
  string id = 1;
  string countryId = 2; //国家
  string countryName = 3; //国家名称
  string provinceCode = 4; //省
  string province = 5; //省
  string cityCode = 6; //市
  string city = 7; //市
  string regionCode = 8; //区
  string region = 9; //区
  string address = 10; //详细地址
  string houseNo = 11; //门牌号
  string postcode = 12; //邮编
  string contactName = 13; //联系人
  string areaCode = 30; //区号
  string phone = 14; //手机号
  bool isDefault = 15; //是否是默认地址
  string referLandmark = 16; //参考地标
  string fullAddress = 17; //完整地址
  common.AddressLabel addressLabel = 18; //地址标签
  string street = 19;
  string addressLabelDesc = 20;
}

message MembershipListResp {
  common.Result result = 1;
  repeated MembershipModel data = 2;
}

// 按权限查询用户的返回
message ListByPrivilegeResp {
  common.Result result = 1;
  ListByPrivilegeModel data = 2;
}

// 按权限查询用户的返回对象
message ListByPrivilegeModel {
  string requestId = 10; //请求ID（前端赋值的请求ID，在查询结果中返回）
  repeated ListByPrivilegeMemberModel memberList = 20;
}

// 按权限查询用户的用户选项信息
message ListByPrivilegeMemberModel {
  string id = 1; // 用户ID
  string username = 2; // 用户名
}