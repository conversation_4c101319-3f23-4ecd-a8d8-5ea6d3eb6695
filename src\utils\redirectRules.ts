/**
 * 重定向规则配置
 * 用于集中管理所有URL重定向规则
 */

export interface RedirectRule {
  redirect:
    | string
    | {
        to: string;
        statusCode: number;
      };
}

export interface RouteRules {
  [key: string]: RedirectRule;
}

/**
 * 所有重定向规则
 * 格式: { 原始路径: { redirect: 目标路径 } }
 */
export const redirectRules: RouteRules = {
  "/article?code=blog1": {
    redirect: {
      to: "/blog/blog1",
      statusCode: 301,
    },
  },
};

/**
 * 获取完整的路由规则，包括API代理规则
 */
export const getFullRouteRules = (): Record<string, any> => {
  return {
    "/api/**": {
      proxy: "http://chilat.mall.dev231.xpormayor.com.mx/api/**",
    },
    ...redirectRules,
  };
};
